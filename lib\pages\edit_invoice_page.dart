import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';

class EditInvoicePage extends StatefulWidget {
  final Invoice invoice;

  const EditInvoicePage({super.key, required this.invoice});

  @override
  State<EditInvoicePage> createState() => _EditInvoicePageState();
}

class _EditInvoicePageState extends State<EditInvoicePage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _clientNameController;
  late TextEditingController _clientNumberController;
  late TextEditingController _productsController;
  late TextEditingController
  _deliveryDetailsController; // Nouveau contrôleur pour les détails de livraison
  late TextEditingController
  _discountController; // Nouveau contrôleur pour la remise
  late TextEditingController
  _tipController; // Nouveau contrôleur pour le pourboire
  late TextEditingController _advanceController;
  late TextEditingController _notesController;
  late TextEditingController _footerNoteController;
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  String? _selectedDeliveryZone;
  String? _selectedLogoPath;
  List<InvoiceItem> _items = [];
  bool _isLoading = false;
  final InvoiceService _invoiceService = InvoiceService();
  // Service d'inventaire supprimé car non utilisé en édition

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeData();
    // Chargement des produits supprimé car non utilisé en édition
  }

  // Méthode _loadStockProducts supprimée car non utilisée en édition

  void _initializeControllers() {
    _clientNameController = TextEditingController(
      text: widget.invoice.clientName,
    );
    _clientNumberController = TextEditingController(
      text: widget.invoice.clientNumber,
    );
    _productsController = TextEditingController(text: widget.invoice.products);
    _deliveryDetailsController = TextEditingController(
      text: widget.invoice.deliveryDetails ?? '',
    ); // Initialisation du contrôleur
    _discountController = TextEditingController(
      text: widget.invoice.discountAmount.toString(),
    ); // Initialisation du contrôleur de remise
    _tipController = TextEditingController(
      text: widget.invoice.tipAmount.toString(),
    ); // Initialisation du contrôleur de pourboire
    _advanceController = TextEditingController(
      text: widget.invoice.advance.toInt().toString(),
    );
    _notesController = TextEditingController(text: widget.invoice.notes ?? '');
    _footerNoteController = TextEditingController(
      text: widget.invoice.footerNote ?? '',
    );
  }

  void _initializeData() {
    _selectedDeliveryZone = widget.invoice.deliveryLocation;
    _selectedLogoPath = widget.invoice.logoPath;

    // Copier correctement tous les items avec toutes leurs propriétés
    _items =
        widget.invoice.items
            .map(
              (item) => InvoiceItem(
                id: item.id,
                name: item.name,
                price: item.price,
                quantity: item.quantity,
                isCustom: item.isCustom,
                categoryName: item.categoryName,
                productId: item.productId,
                isFromStock: item.isFromStock,
              ),
            )
            .toList();

    // S'assurer qu'il y a au moins un item
    if (_items.isEmpty) {
      _addNewItem();
    }
  }

  void _addNewItem() {
    setState(() {
      _items.insert(
        0,
        InvoiceItem(
          id: _invoiceService.generateItemId(),
          name: '',
          price: 0,
          quantity: 1,
          isCustom: false,
        ),
      );
    });
  }

  void _removeItem(int index) {
    if (_items.length > 1) {
      setState(() {
        _items.removeAt(index);
      });
    }
  }

  void _updateItem(int index, InvoiceItem updatedItem) {
    setState(() {
      _items[index] = updatedItem;
    });
  }

  double get _subtotal => _invoiceService.calculateSubtotal(_items);
  double get _deliveryPrice =>
      _selectedDeliveryZone != null
          ? DeliveryZones.getDeliveryPrice(_selectedDeliveryZone!)
          : 0;
  double get _advance => double.tryParse(_advanceController.text) ?? 0;
  double get _discount =>
      double.tryParse(_discountController.text) ?? 0; // Getter pour la remise
  double get _tip =>
      double.tryParse(_tipController.text) ?? 0; // Getter pour le pourboire
  double get _total => _invoiceService.calculateTotal(
    subtotal: _subtotal,
    deliveryPrice: _deliveryPrice,
    discountAmount: _discount, // Ajout de la remise au calcul
    tipAmount: _tip, // Ajout du pourboire au calcul
    advance: _advance,
  );

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;
    if (_items.isEmpty || _items.every((item) => item.name.isEmpty)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez ajouter au moins un article')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final updatedInvoice = widget.invoice.copyWith(
        clientName: _clientNameController.text.trim(),
        clientNumber: _clientNumberController.text.trim(),
        products: _productsController.text.trim(),
        items: _items.where((item) => item.name.isNotEmpty).toList(),
        deliveryLocation: _selectedDeliveryZone ?? '',
        deliveryDetails:
            _deliveryDetailsController.text.trim().isEmpty
                ? null
                : _deliveryDetailsController.text
                    .trim(), // Ajout des détails de livraison
        discountAmount: _discount, // Ajout de la remise
        tipAmount: _tip, // Ajout du pourboire
        deliveryPrice: _deliveryPrice,
        advance: _advance,
        subtotal: _subtotal,
        total: _total,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        logoPath: _selectedLogoPath,
        footerNote:
            _footerNoteController.text.trim().isEmpty
                ? null
                : _footerNoteController.text.trim(),
      );

      await _invoiceService.updateInvoice(updatedInvoice);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Facture modifiée avec succès')),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la modification: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Widget _buildItemCard(int index) {
    final item = _items[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color:
          index == 0 ? Colors.lightBlue[50] : null, // Bleu clair pour Article 1
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Article ${index + 1}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_items.length > 1)
                  IconButton(
                    onPressed: () => _removeItem(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    tooltip: 'Supprimer cet article',
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Choix entre catégorie prédéfinie et article personnalisé
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Catégorie prédéfinie'),
                    value: false,
                    groupValue: item.isCustom,
                    onChanged: (value) {
                      _updateItem(
                        index,
                        item.copyWith(isCustom: value!, name: '', price: 0),
                      );
                    },
                    dense: true,
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Article personnalisé'),
                    value: true,
                    groupValue: item.isCustom,
                    onChanged: (value) {
                      _updateItem(
                        index,
                        item.copyWith(isCustom: value!, name: '', price: 0),
                      );
                    },
                    dense: true,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (!item.isCustom)
              // Sélection de catégorie prédéfinie
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Catégorie',
                  border: OutlineInputBorder(),
                ),
                value: item.name.isEmpty ? null : item.name,
                items:
                    PredefinedCategories.categories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category['name'],
                        child: Text(
                          '${category['name']} - ${_currencyFormat.format(category['price'])} FCFA',
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    final category = PredefinedCategories.categories.firstWhere(
                      (cat) => cat['name'] == value,
                    );
                    _updateItem(
                      index,
                      item.copyWith(
                        name: value,
                        price: category['price'].toDouble(),
                        categoryName: value,
                      ),
                    );
                  }
                },
                validator:
                    (value) =>
                        value == null ? 'Sélectionnez une catégorie' : null,
              )
            else
              // Article personnalisé
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Nom de l\'article',
                        border: OutlineInputBorder(),
                      ),
                      initialValue: item.name,
                      onChanged: (value) {
                        _updateItem(index, item.copyWith(name: value));
                      },
                      validator:
                          (value) =>
                              value?.isEmpty == true ? 'Nom requis' : null,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Prix (FCFA)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      initialValue:
                          item.price > 0 ? item.price.toInt().toString() : '',
                      onChanged: (value) {
                        final price = double.tryParse(value) ?? 0;
                        _updateItem(index, item.copyWith(price: price));
                      },
                      validator: (value) {
                        final price = double.tryParse(value ?? '');
                        return price == null || price <= 0
                            ? 'Prix invalide'
                            : null;
                      },
                    ),
                  ),
                ],
              ),

            const SizedBox(height: 16),

            // Quantité et total
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Quantité',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    initialValue: item.quantity.toString(),
                    onChanged: (value) {
                      final quantity = int.tryParse(value) ?? 1;
                      _updateItem(index, item.copyWith(quantity: quantity));
                    },
                    validator: (value) {
                      final quantity = int.tryParse(value ?? '');
                      return quantity == null || quantity <= 0
                          ? 'Quantité invalide'
                          : null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Total article',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        Text(
                          '${_currencyFormat.format(item.total)} FCFA',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalSection() {
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'RÉCAPITULATIF',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTotalRow(
              'Sous-total:',
              '${_currencyFormat.format(_subtotal)} FCFA',
            ),
            _buildTotalRow(
              'Livraison:',
              '${_currencyFormat.format(_deliveryPrice)} FCFA',
            ),
            if (_tip > 0)
              _buildTotalRow(
                'Pourboire:',
                '+${_currencyFormat.format(_tip)} FCFA',
              ),
            if (_discount > 0)
              _buildTotalRow(
                'Remise:',
                '-${_currencyFormat.format(_discount)} FCFA',
              ),
            if (_advance > 0)
              _buildTotalRow(
                'Avance:',
                '-${_currencyFormat.format(_advance)} FCFA',
              ),
            const Divider(),
            _buildTotalRow(
              'TOTAL:',
              '${_currencyFormat.format(_total)} FCFA',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.blue : null,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Modifier ${widget.invoice.invoiceNumber}'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Informations de base
            Card(
              color: Colors.grey[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'INFORMATIONS DE BASE',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'N° Facture: ${widget.invoice.invoiceNumber}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    Text(
                      'Créée le: ${DateFormat('dd/MM/yyyy à HH:mm').format(widget.invoice.createdAt)}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Informations client
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'INFORMATIONS CLIENT',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _clientNameController,
                      decoration: const InputDecoration(
                        labelText: 'Nom du client *',
                        border: OutlineInputBorder(),
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty == true ? 'Nom requis' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _clientNumberController,
                      decoration: const InputDecoration(
                        labelText: 'Numéro de téléphone *',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.phone,
                      validator:
                          (value) =>
                              value?.isEmpty == true ? 'Numéro requis' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _productsController,
                      decoration: const InputDecoration(
                        labelText: 'Description générale de la commande *',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      validator:
                          (value) =>
                              value?.isEmpty == true
                                  ? 'Description requise'
                                  : null,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Articles
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'ARTICLES',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: _addNewItem,
                  icon: const Icon(Icons.add),
                  label: const Text('Ajouter'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[900],
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            ..._items.asMap().entries.map((entry) => _buildItemCard(entry.key)),

            const SizedBox(height: 16),

            // Livraison et avance
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'LIVRAISON ET PAIEMENT',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Zone de livraison *',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedDeliveryZone,
                      items:
                          DeliveryZones.availableZones.map((zone) {
                            return DropdownMenuItem<String>(
                              value: zone,
                              child: Text(
                                '$zone - ${_currencyFormat.format(DeliveryZones.getDeliveryPrice(zone))} FCFA',
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedDeliveryZone = value;
                        });
                      },
                      validator:
                          (value) =>
                              value == null
                                  ? 'Zone de livraison requise'
                                  : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _deliveryDetailsController,
                      decoration: const InputDecoration(
                        labelText: 'Détails de livraison (optionnel)',
                        border: OutlineInputBorder(),
                        hintText: 'Ex: Numéro de bureau, étage, interphone...',
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _discountController,
                      decoration: const InputDecoration(
                        labelText: 'Remise (optionnel)',
                        hintText: 'Montant de la remise',
                        border: OutlineInputBorder(),
                        prefixText: 'FCFA ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*$'),
                        ),
                      ],
                      onChanged:
                          (_) => setState(
                            () {},
                          ), // Recalculer le total lors de la saisie
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _tipController,
                      decoration: const InputDecoration(
                        labelText: 'Pourboire (optionnel)',
                        hintText: 'Montant du pourboire',
                        border: OutlineInputBorder(),
                        prefixText: 'FCFA ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*$'),
                        ),
                      ],
                      onChanged:
                          (_) => setState(
                            () {},
                          ), // Recalculer le total lors de la saisie
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _advanceController,
                      decoration: const InputDecoration(
                        labelText: 'Avance (FCFA)',
                        border: OutlineInputBorder(),
                        hintText: '0',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      onChanged: (value) => setState(() {}),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes (optionnel)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    // Sélection du logo
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _selectedLogoPath != null
                                ? 'Logo sélectionné: ${_selectedLogoPath!.split('\\').last}'
                                : 'Aucun logo sélectionné',
                            style: TextStyle(
                              color:
                                  _selectedLogoPath != null
                                      ? Colors.green
                                      : Colors.grey,
                            ),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _selectLogo,
                          icon: const Icon(Icons.image),
                          label: const Text('Choisir logo'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[900],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _footerNoteController,
                      decoration: const InputDecoration(
                        labelText: 'Note de bas de page (optionnel)',
                        border: OutlineInputBorder(),
                        hintText: 'Ex: Merci pour votre confiance - HCP-DESIGN',
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Récapitulatif
            _buildTotalSection(),

            const SizedBox(height: 24),

            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Annuler'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveChanges,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[900],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child:
                        _isLoading
                            ? const CircularProgressIndicator(
                              color: Colors.white,
                            )
                            : const Text(
                              'SAUVEGARDER LES MODIFICATIONS',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectLogo() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedLogoPath = result.files.single.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sélection du logo: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _clientNameController.dispose();
    _clientNumberController.dispose();
    _productsController.dispose();
    _advanceController.dispose();
    _notesController.dispose();
    _footerNoteController.dispose();
    super.dispose();
  }
}
