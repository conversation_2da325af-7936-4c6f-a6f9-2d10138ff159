import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import '../models/colis.dart';
import '../services/colis_service.dart';
import '../services/ocr_service.dart';
import 'confirmation_livraison_page.dart';

class AjouterLivraisonPage extends StatefulWidget {
  final Colis? colis;
  const AjouterLivraisonPage({super.key, this.colis});

  @override
  State<AjouterLivraisonPage> createState() => _AjouterLivraisonPageState();
}

class _AjouterLivraisonPageState extends State<AjouterLivraisonPage> {
  final _formKey = GlobalKey<FormState>();
  final _libelleController = TextEditingController();
  final _numeroClientController = TextEditingController();
  final _nomClientController = TextEditingController();
  final _resteAPayerController = TextEditingController();
  final _adresseLivraisonController = TextEditingController();
  final _notesController = TextEditingController();
  final _fraisLivraisonController = TextEditingController();

  final ColisService _colisService = ColisService.instance;
  final ImagePicker _picker = ImagePicker();
  final OcrService _ocrService = OcrService.instance;

  String? _selectedZone;
  StatutLivraison _statutInitial = StatutLivraison.livree;
  File? _imageFile;
  bool _isLoading = false;
  bool _isOcrProcessing = false;
  Map<String, String> _ocrData = {};

  // Variables pour la rotation et le zoom de l'image
  double _imageRotation = 0.0; // Angle de rotation en radians
  late TransformationController _transformationController;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    if (widget.colis != null) {
      final c = widget.colis!;
      _libelleController.text = c.libelle;
      _numeroClientController.text = c.numeroClient;
      _nomClientController.text = c.nomClient ?? '';
      _resteAPayerController.text = c.resteAPayer.toString();
      _adresseLivraisonController.text = c.adresseLivraison ?? '';
      _notesController.text = c.notes ?? '';
      _fraisLivraisonController.text = c.fraisLivraison.toString();
      _selectedZone = c.zoneLivraison;
      _statutInitial = c.statut;
      if (c.photoPath.isNotEmpty) {
        _imageFile = File(c.photoPath);
      }
    }
  }

  @override
  void dispose() {
    _libelleController.dispose();
    _numeroClientController.dispose();
    _nomClientController.dispose();
    _resteAPayerController.dispose();
    _adresseLivraisonController.dispose();
    _notesController.dispose();
    _fraisLivraisonController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  Future<void> _prendrePhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _imageFile = File(image.path);
          // Réinitialiser les transformations d'image
          _imageRotation = 0.0;
          _transformationController.value = Matrix4.identity();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la prise de photo: $e')),
        );
      }
    }
  }

  Future<void> _choisirImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _imageFile = File(image.path);
          // Réinitialiser les transformations d'image
          _imageRotation = 0.0;
          _transformationController.value = Matrix4.identity();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sélection d\'image: $e')),
        );
      }
    }
  }

  void _supprimerImage() {
    setState(() {
      _imageFile = null;
      // Réinitialiser les transformations d'image
      _imageRotation = 0.0;
      _transformationController.value = Matrix4.identity();
    });
  }

  void _onZoneChanged(String? zone) {
    if (zone != null) {
      setState(() {
        _selectedZone = zone;
        // Suppression de l'assignation automatique des prix
        // L'utilisateur doit maintenant saisir manuellement les frais de livraison
      });
    }
  }

  Future<void> _enregistrerLivraison() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_imageFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez ajouter une photo du colis')),
      );
      return;
    }

    if (_selectedZone == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner une zone de livraison'),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.colis == null) {
        // Création
        final colisId = await _colisService.ajouterColis(
          libelle: _libelleController.text.trim(),
          photoPath: _imageFile!.path,
          zoneLivraison: _selectedZone!,
          numeroClient: _numeroClientController.text.trim(),
          resteAPayer: double.parse(
            _resteAPayerController.text.replaceAll(',', ''),
          ),
          fraisLivraison: double.parse(_fraisLivraisonController.text.trim()),
          statut: _statutInitial,
          nomClient:
              _nomClientController.text.trim().isEmpty
                  ? null
                  : _nomClientController.text.trim(),
          adresseLivraison:
              _adresseLivraisonController.text.trim().isEmpty
                  ? null
                  : _adresseLivraisonController.text.trim(),
          notes:
              _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
        );

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => ConfirmationLivraisonPage(colisId: colisId),
            ),
          );
        }
      } else {
        // Modification
        final colisModifie = widget.colis!.copyWith(
          libelle: _libelleController.text.trim(),
          photoPath: _imageFile!.path,
          zoneLivraison: _selectedZone!,
          numeroClient: _numeroClientController.text.trim(),
          resteAPayer: double.parse(
            _resteAPayerController.text.replaceAll(',', ''),
          ),
          fraisLivraison: double.parse(_fraisLivraisonController.text.trim()),
          statut: _statutInitial,
          nomClient:
              _nomClientController.text.trim().isEmpty
                  ? null
                  : _nomClientController.text.trim(),
          adresseLivraison:
              _adresseLivraisonController.text.trim().isEmpty
                  ? null
                  : _adresseLivraisonController.text.trim(),
          notes:
              _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
        );
        await _colisService.updateColis(colisModifie);
        if (mounted) {
          Navigator.of(context).pop(colisModifie);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'enregistrement: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sauvegarderEnBrouillon() async {
    // Pour un brouillon, seule la photo est obligatoire
    if (_imageFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Veuillez ajouter une photo du colis pour créer un brouillon',
          ),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _colisService.ajouterColis(
        libelle:
            _libelleController.text.trim().isEmpty
                ? 'Brouillon - 24{DateTime.now().day}/24{DateTime.now().month}/24{DateTime.now().year}'
                : _libelleController.text.trim(),
        photoPath: _imageFile!.path,
        zoneLivraison: _selectedZone ?? 'Cocody', // Zone par défaut
        numeroClient:
            _numeroClientController.text.trim().isEmpty
                ? 'À compléter'
                : _numeroClientController.text.trim(),
        resteAPayer:
            _resteAPayerController.text.trim().isEmpty
                ? 0.0
                : double.tryParse(
                      _resteAPayerController.text.replaceAll(',', ''),
                    ) ??
                    0.0,
        fraisLivraison:
            _fraisLivraisonController.text.trim().isEmpty
                ? 0.0
                : double.tryParse(_fraisLivraisonController.text.trim()) ?? 0.0,
        statut: StatutLivraison.brouillon,
        nomClient:
            _nomClientController.text.trim().isEmpty
                ? null
                : _nomClientController.text.trim(),
        adresseLivraison:
            _adresseLivraisonController.text.trim().isEmpty
                ? null
                : _adresseLivraisonController.text.trim(),
        notes:
            _notesController.text.trim().isEmpty
                ? 'Brouillon - Informations à compléter'
                : _notesController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Brouillon sauvegardé avec succès! Vous pourrez le compléter plus tard.',
            ),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.of(context).pop(); // Retour à la page précédente
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sauvegarde du brouillon: $e'),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildPhotoSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.camera_alt, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'Photo du colis',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const Text(' *', style: TextStyle(color: Colors.red)),
              ],
            ),
            const SizedBox(height: 16),
            if (_imageFile != null) ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: InteractiveViewer(
                    transformationController: _transformationController,
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 4.0,
                    child: Transform.rotate(
                      angle: _imageRotation,
                      child: Image.file(
                        _imageFile!,
                        fit: BoxFit.contain,
                        width: double.infinity,
                        height: double.infinity,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              // Contrôles d'image (rotation et zoom)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // Rotation
                        Column(
                          children: [
                            Text(
                              'Rotation',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: _rotateImageLeft,
                                  icon: const Icon(Icons.rotate_left),
                                  tooltip: 'Rotation gauche',
                                  style: IconButton.styleFrom(
                                    backgroundColor: Colors.blue[50],
                                    foregroundColor: Colors.blue[600],
                                  ),
                                ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: _rotateImageRight,
                                  icon: const Icon(Icons.rotate_right),
                                  tooltip: 'Rotation droite',
                                  style: IconButton.styleFrom(
                                    backgroundColor: Colors.blue[50],
                                    foregroundColor: Colors.blue[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        // Zoom
                        Column(
                          children: [
                            Text(
                              'Zoom',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: _zoomOut,
                                  icon: const Icon(Icons.zoom_out),
                                  tooltip: 'Zoom -',
                                  style: IconButton.styleFrom(
                                    backgroundColor: Colors.green[50],
                                    foregroundColor: Colors.green[600],
                                  ),
                                ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: _zoomIn,
                                  icon: const Icon(Icons.zoom_in),
                                  tooltip: 'Zoom +',
                                  style: IconButton.styleFrom(
                                    backgroundColor: Colors.green[50],
                                    foregroundColor: Colors.green[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        // Reset
                        Column(
                          children: [
                            Text(
                              'Reset',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 4),
                            IconButton(
                              onPressed: _resetImageTransform,
                              icon: const Icon(Icons.refresh),
                              tooltip: 'Réinitialiser',
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.orange[50],
                                foregroundColor: Colors.orange[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: _prendrePhoto,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Reprendre'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _supprimerImage,
                    icon: const Icon(Icons.delete),
                    label: const Text('Supprimer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),

              // Bouton OCR - Toujours visible quand une image est présente
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isOcrProcessing ? null : _analyserImageOCR,
                  icon:
                      _isOcrProcessing
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Icon(Icons.text_fields),
                  label: Text(
                    _isOcrProcessing
                        ? 'Analyse en cours...'
                        : 'Remplir automatiquement (OCR)',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ] else ...[
              Container(
                width: double.infinity,
                height: 150,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    style: BorderStyle.solid,
                  ),
                  color: Colors.grey[50],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add_a_photo, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      'Aucune photo sélectionnée',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: _prendrePhoto,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Prendre photo'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _choisirImage,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Galerie'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.auto_awesome, color: Colors.orange[600]),
                        const SizedBox(width: 8),
                        const Text(
                          'Données détectées par OCR',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    ..._ocrData.entries
                        .where((e) => e.key != 'texte_complet')
                        .map(
                          (entry) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    '${entry.key}:',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                Expanded(flex: 3, child: Text(entry.value)),
                              ],
                            ),
                          ),
                        ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _appliquerDonneesOCR,
                            icon: const Icon(Icons.check),
                            label: const Text('Appliquer'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green[600],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              setState(() {
                                _ocrData.clear();
                              });
                            },
                            icon: const Icon(Icons.close),
                            label: const Text('Ignorer'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[600],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthodes pour la manipulation d'image
  void _rotateImageLeft() {
    setState(() {
      _imageRotation -= 1.5708; // -90 degrés en radians
    });
  }

  void _rotateImageRight() {
    setState(() {
      _imageRotation += 1.5708; // +90 degrés en radians
    });
  }

  void _zoomIn() {
    final Matrix4 matrix = _transformationController.value.clone();
    final double currentScale = matrix.getMaxScaleOnAxis();
    if (currentScale < 4.0) {
      matrix.scale(1.2);
      _transformationController.value = matrix;
    }
  }

  void _zoomOut() {
    final Matrix4 matrix = _transformationController.value.clone();
    final double currentScale = matrix.getMaxScaleOnAxis();
    if (currentScale > 0.5) {
      matrix.scale(1 / 1.2);
      _transformationController.value = matrix;
    }
  }

  void _resetImageTransform() {
    setState(() {
      _imageRotation = 0.0;
    });
    _transformationController.value = Matrix4.identity();
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool required = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    int maxLines = 1,
    String? suffixText,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label + (required ? ' *' : ''),
        prefixIcon: Icon(icon),
        suffixText: suffixText,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator:
          required
              ? (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Ce champ est obligatoire';
                }
                return validator?.call(value);
              }
              : validator,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.colis == null
              ? 'Ajouter une livraison'
              : 'Modifier une livraison',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Enregistrement en cours...'),
                  ],
                ),
              )
              : Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    // Section photo
                    _buildPhotoSection(),

                    // Afficher les données OCR extraites
                    if (_ocrData.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.auto_awesome,
                                    color: Colors.orange[600],
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Données détectées par OCR',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              ..._ocrData.entries
                                  .where((e) => e.key != 'texte_complet')
                                  .map(
                                    (entry) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 4,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              '${entry.key}:',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 3,
                                            child: Text(entry.value),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: _appliquerDonneesOCR,
                                      icon: const Icon(Icons.check),
                                      label: const Text('Appliquer'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green[600],
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () {
                                        setState(() {
                                          _ocrData.clear();
                                        });
                                      },
                                      icon: const Icon(Icons.close),
                                      label: const Text('Ignorer'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.grey[600],
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    const SizedBox(height: 16),

                    // Informations client
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.person, color: Colors.blue[600]),
                                const SizedBox(width: 8),
                                const Text(
                                  'Informations client',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            _buildFormField(
                              controller: _numeroClientController,
                              label: 'Numéro du client',
                              icon: Icons.phone,
                              required: true,
                              keyboardType: TextInputType.phone,
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  if (value.length < 8) {
                                    return 'Le numéro doit contenir au moins 8 chiffres';
                                  }
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            _buildFormField(
                              controller: _nomClientController,
                              label: 'Nom du client',
                              icon: Icons.person_outline,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Informations du colis
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.inventory_2,
                                  color: Colors.blue[600],
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Informations du colis',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            _buildFormField(
                              controller: _libelleController,
                              label: 'Libellé du colis',
                              icon: Icons.label,
                              required: true,
                            ),

                            const SizedBox(height: 16),

                            // Zone de livraison
                            DropdownButtonFormField<String>(
                              value: _selectedZone,
                              decoration: InputDecoration(
                                labelText: 'Zone de livraison *',
                                prefixIcon: const Icon(Icons.location_on),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                filled: true,
                                fillColor: Colors.grey[50],
                              ),
                              items:
                                  ZoneLivraison.getZonesDisponibles()
                                      .map(
                                        (zone) => DropdownMenuItem(
                                          value: zone,
                                          child: Text(zone),
                                        ),
                                      )
                                      .toList(),
                              onChanged: _onZoneChanged,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Veuillez sélectionner une zone';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // Frais de livraison (éditable)
                            _buildFormField(
                              controller: _fraisLivraisonController,
                              label: 'Frais de livraison',
                              icon: Icons.local_shipping,
                              required: true,
                              keyboardType: TextInputType.number,
                              suffixText: 'FCFA',
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  final amount = double.tryParse(value);
                                  if (amount == null || amount < 0) {
                                    return 'Veuillez entrer un montant valide';
                                  }
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            _buildFormField(
                              controller: _adresseLivraisonController,
                              label: 'Adresse de livraison',
                              icon: Icons.home,
                              maxLines: 2,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Informations financières
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.monetization_on,
                                  color: Colors.blue[600],
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Informations financières',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            _buildFormField(
                              controller: _resteAPayerController,
                              label: 'Reste à payer',
                              icon: Icons.account_balance_wallet,
                              required: true,
                              keyboardType: TextInputType.number,
                              suffixText: 'FCFA',
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  final amount = double.tryParse(
                                    value.replaceAll(',', ''),
                                  );
                                  if (amount == null || amount < 0) {
                                    return 'Veuillez entrer un montant valide';
                                  }
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Statut initial
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.flag, color: Colors.blue[600]),
                                const SizedBox(width: 8),
                                const Text(
                                  'Statut initial',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            DropdownButtonFormField<StatutLivraison>(
                              value: _statutInitial,
                              decoration: InputDecoration(
                                labelText: 'Statut',
                                prefixIcon: const Icon(Icons.flag_outlined),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                filled: true,
                                fillColor: Colors.grey[50],
                              ),
                              items:
                                  StatutLivraison.values
                                      .map(
                                        (statut) => DropdownMenuItem(
                                          value: statut,
                                          child: Row(
                                            children: [
                                              Text(statut.emoji),
                                              const SizedBox(width: 8),
                                              Text(statut.libelle),
                                            ],
                                          ),
                                        ),
                                      )
                                      .toList(),
                              onChanged: (StatutLivraison? statut) {
                                if (statut != null) {
                                  setState(() {
                                    _statutInitial = statut;
                                  });
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Notes
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.note, color: Colors.blue[600]),
                                const SizedBox(width: 8),
                                const Text(
                                  'Notes supplémentaires',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            _buildFormField(
                              controller: _notesController,
                              label: 'Notes (optionnel)',
                              icon: Icons.note_outlined,
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Boutons d'enregistrement
                    Row(
                      children: [
                        // Bouton Brouillon
                        Expanded(
                          child: SizedBox(
                            height: 50,
                            child: ElevatedButton.icon(
                              onPressed: _sauvegarderEnBrouillon,
                              icon: const Icon(Icons.drafts),
                              label: const Text(
                                'Brouillon',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange[600],
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Bouton Enregistrer
                        Expanded(
                          flex: 2,
                          child: SizedBox(
                            height: 50,
                            child: ElevatedButton.icon(
                              onPressed: _enregistrerLivraison,
                              icon: Icon(
                                widget.colis == null ? Icons.save : Icons.edit,
                              ),
                              label: Text(
                                widget.colis == null
                                    ? 'Enregistrer'
                                    : 'Modifier',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green[600],
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
    );
  }

  // ===== MÉTHODES OCR =====

  /// Analyser l'image avec OCR pour extraire les données du colis
  Future<void> _analyserImageOCR() async {
    if (_imageFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Aucune image sélectionnée')),
      );
      return;
    }

    setState(() {
      _isOcrProcessing = true;
      _ocrData.clear();
    });

    try {
      final ocrData = await _ocrService.analyzePackageImage(_imageFile!.path);

      setState(() {
        _ocrData = ocrData;
        _isOcrProcessing = false;
      });

      if (mounted) {
        if (ocrData.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${ocrData.length - 1} données détectées ! Vérifiez et appliquez.',
              ),
              backgroundColor: Colors.green[600],
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Aucune donnée détectée. Essayez avec une image plus claire.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isOcrProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur OCR: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Appliquer les données OCR aux champs du formulaire
  void _appliquerDonneesOCR() {
    if (_ocrData.isEmpty) return;

    // Mapping des données OCR vers les champs du formulaire
    if (_ocrData.containsKey('libelle') && _ocrData['libelle']!.isNotEmpty) {
      _libelleController.text = _ocrData['libelle']!;
    }

    if (_ocrData.containsKey('nomClient') &&
        _ocrData['nomClient']!.isNotEmpty) {
      _nomClientController.text = _ocrData['nomClient']!;
    }

    if (_ocrData.containsKey('numeroClient') &&
        _ocrData['numeroClient']!.isNotEmpty) {
      _numeroClientController.text = _ocrData['numeroClient']!;
    }

    if (_ocrData.containsKey('adresseLivraison') &&
        _ocrData['adresseLivraison']!.isNotEmpty) {
      _adresseLivraisonController.text = _ocrData['adresseLivraison']!;
    }

    if (_ocrData.containsKey('resteAPayer') &&
        _ocrData['resteAPayer']!.isNotEmpty) {
      _resteAPayerController.text = _ocrData['resteAPayer']!;
    }

    if (_ocrData.containsKey('fraisLivraison') &&
        _ocrData['fraisLivraison']!.isNotEmpty) {
      _fraisLivraisonController.text = _ocrData['fraisLivraison']!;
    }

    if (_ocrData.containsKey('notes') && _ocrData['notes']!.isNotEmpty) {
      _notesController.text = _ocrData['notes']!;
    }

    // Effacer les données OCR après application
    setState(() {
      _ocrData.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Données appliquées avec succès !'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
